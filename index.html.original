<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <title>网易云音乐播放器 - 搜索列表 + 单曲APlayer + 翻页</title>
  <link rel="stylesheet" href="https://mirrors.sustech.edu.cn/cdnjs/ajax/libs/aplayer/1.10.1/APlayer.min.css" />
  <style>
    body {
      background-color: #1e1e1e;
      color: #d4d4d4;
      font-family: "Segoe UI", sans-serif;
      padding: 20px;
      margin: 0;
    }
    .container {
      max-width: 700px;
      margin: 0 auto;
    }
    h2 {
      text-align: center;
      margin-bottom: 10px;
    }
    #searchBar {
      text-align: center;
      margin-bottom: 20px;
    }
    #keyword {
      width: 300px;
      padding: 8px;
      font-size: 16px;
      border-radius: 4px;
      border: none;
      background-color: #2d2d2d;
      color: #d4d4d4;
    }
    #searchBtn {
      padding: 8px 15px;
      font-size: 16px;
      margin-left: 8px;
      cursor: pointer;
      border: none;
      border-radius: 4px;
      background-color: #3a3a3a;
      color: #d4d4d4;
      transition: background-color 0.3s;
    }
    #searchBtn:hover {
      background-color: #5a5a5a;
    }
    #results {
      margin-top: 10px;
      border-radius: 6px;
      background: #2b2b2b;
      overflow: hidden;
      box-shadow: 0 0 10px #111;
      min-height: 50px;
    }
    .song {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 15px;
      border-bottom: 1px solid #444;
      font-size: 14px;
    }
    .song:last-child {
      border-bottom: none;
    }
    .song-info {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 65%;
      color: #ddd;
    }
    .song-info b {
      font-weight: 600;
      margin-right: 6px;
    }
    .btn-group button {
      margin-left: 8px;
      padding: 6px 12px;
      font-size: 14px;
      border: none;
      border-radius: 4px;
      background-color: #3a3a3a;
      color: #d4d4d4;
      cursor: pointer;
      transition: background-color 0.3s;
      user-select: none;
    }
    .btn-group button:hover {
      background-color: #5a5a5a;
    }
    #pagination {
      margin-top: 15px;
      text-align: center;
      user-select: none;
      font-size: 14px;
    }
    #pagination button {
      padding: 6px 12px;
      margin: 0 8px;
      cursor: pointer;
      border: none;
      border-radius: 4px;
      background-color: #3a3a3a;
      color: #d4d4d4;
      transition: background-color 0.3s;
    }
    #pagination button:hover:not(:disabled) {
      background-color: #5a5a5a;
    }
    #pagination button:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
    
    /* 修复歌词字体显示 - 使用更高优先级的选择器覆盖默认样式 */
    .aplayer.aplayer-withlrc .aplayer-info .aplayer-music .aplayer-title {
      font-size: 16px !important;
      font-weight: bold !important;
      color: #d4d4d4 !important;
    }
    .aplayer.aplayer-withlrc .aplayer-info .aplayer-music .aplayer-author {
      font-size: 14px !important;
      color: #999 !important;
    }
    
    /* 歌词容器样式覆盖 */
    .aplayer.aplayer-withlrc .aplayer-lrc {
      text-align: center !important;
      margin: -10px 0 7px !important;
      height: 30px !important;
      background: transparent !important;
      font-family: "Segoe UI", "Microsoft YaHei", Arial, sans-serif !important;
    }
    
    /* 歌词渐变背景覆盖 */
    .aplayer.aplayer-withlrc .aplayer-lrc:before,
    .aplayer.aplayer-withlrc .aplayer-lrc:after {
      display: none !important;
    }
    
    /* 歌词文本样式覆盖 */
    .aplayer.aplayer-withlrc .aplayer-lrc p {
      font-size: 13px !important;
      color: #888 !important;
      line-height: 16px !important;
      height: 16px !important;
      padding: 0 !important;
      margin: 0 !important;
      transition: all 0.5s ease-out !important;
      opacity: 0.6 !important;
      overflow: hidden !important;
      font-family: "Segoe UI", "Microsoft YaHei", Arial, sans-serif !important;
      font-weight: normal !important;
      text-shadow: none !important;
    }
    
    /* 当前播放歌词高亮样式 */
    .aplayer.aplayer-withlrc .aplayer-lrc p.aplayer-lrc-current {
      opacity: 1 !important;
      overflow: visible !important;
      height: initial !important;
      min-height: 16px !important;
      font-size: 15px !important;
      font-weight: bold !important;
      color: #00d4ff !important;
      text-shadow: 0 0 5px rgba(0, 212, 255, 0.3) !important;
    }
    
    /* 歌词内容容器 */
    .aplayer.aplayer-withlrc .aplayer-lrc .aplayer-lrc-contents {
      width: 100% !important;
      transition: all 0.5s ease-out !important;
      user-select: text !important;
      cursor: default !important;
    }
    
    /* 播放器主体样式适配暗色主题 */
    .aplayer {
      background: #2b2b2b !important;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
      border-radius: 6px !important;
    }
    
    /* 播放器信息区域暗色适配 */
    .aplayer .aplayer-info {
      background: transparent !important;
    }
    
    /* 进度条暗色适配 */
    .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar {
      background: #444 !important;
    }
    
    .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-loaded {
      background: #666 !important;
    }
    
    .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played {
      background: #00d4ff !important;
    }
    
    .aplayer .aplayer-info .aplayer-controller .aplayer-bar-wrap .aplayer-bar .aplayer-played .aplayer-thumb {
      background: #00d4ff !important;
    }
    
    /* 时间显示暗色适配 */
    .aplayer .aplayer-info .aplayer-controller .aplayer-time {
      color: #ccc !important;
    }
  </style>
</head>
<body>
  <div class="container">
    <h2>网易云音乐播放器</h2>
    <div id="searchBar">
      <input type="text" id="keyword" placeholder="请输入歌曲关键词" autocomplete="off" />
      <button id="searchBtn">搜索</button>
    </div>

    <div id="results"></div>

    <div id="pagination" style="display:none;">
      <button id="prevPageBtn">上一页</button>
      <span id="pageInfo"></span>
      <button id="nextPageBtn">下一页</button>
    </div>

    <div id="aplayer"></div>
  </div>

  <script src="https://mirrors.sustech.edu.cn/cdnjs/ajax/libs/aplayer/1.10.1/APlayer.min.js"></script>
  <script>
    let aplayer = null;
    let currentPage = 1;
    let currentKeyword = '';
    let lastResultCount = 0;
    let currentPlaylist = []; // 存储当前播放列表
    let currentSongIndex = 0; // 当前播放的歌曲索引
    
    const resultsDiv = document.getElementById("results");
    const paginationDiv = document.getElementById("pagination");
    const pageInfoSpan = document.getElementById("pageInfo");
    const prevPageBtn = document.getElementById("prevPageBtn");
    const nextPageBtn = document.getElementById("nextPageBtn");
    
    document.getElementById('searchBtn').addEventListener('click', () => search(1));
    document.getElementById('keyword').addEventListener('keydown', e => {
      if (e.key === 'Enter') search(1);
    });

    prevPageBtn.addEventListener('click', () => {
      if (currentPage > 1) {
        search(currentPage - 1);
      }
    });
    
    nextPageBtn.addEventListener('click', () => {
      if (lastResultCount === 10) {
        search(currentPage + 1);
      }
    });

    async function search(page = 1) {
      const kw = document.getElementById("keyword").value.trim();
      if (!kw) {
        alert("请输入关键词");
        return;
      }
      currentKeyword = kw;
      currentPage = page;

      resultsDiv.innerHTML = '';
      paginationDiv.style.display = 'none';

      try {
        const res = await fetch(`/search?q=${encodeURIComponent(kw)}&page=${page}`);
        if (!res.ok) {
          resultsDiv.innerHTML = "搜索失败，请稍后再试。";
          return;
        }
        const songs = await res.json();
        lastResultCount = songs.length;
        currentPlaylist = songs; // 更新当前播放列表

        if (songs.length === 0) {
          resultsDiv.innerHTML = "没有找到歌曲";
          return;
        }

        songs.forEach((song, i) => {
          const artists = song.artists.map(a => a.name).join(', ');
          const link = `https://music.163.com?id=${song.id}`;

          const div = document.createElement('div');
          div.className = 'song';

          div.innerHTML = `
            <div class="song-info" title="${song.name} — ${artists}">
              <b>${song.name}</b> - ${artists}
            </div>
            <div class="btn-group">
              <button onclick="playSong(${i})">▶️ 播放</button>
              <button onclick="copyLink('${link}', this)">复制链接</button>
            </div>
          `;

          resultsDiv.appendChild(div);
        });

        paginationDiv.style.display = 'block';
        pageInfoSpan.textContent = `第 ${currentPage} 页`;
        prevPageBtn.disabled = currentPage === 1;
        nextPageBtn.disabled = lastResultCount < 10;
      } catch (e) {
        console.error(e);
        resultsDiv.innerHTML = "搜索异常，请检查控制台";
      }
    }

    async function playSong(index) {
      if (index >= currentPlaylist.length) {
        console.log("播放列表已结束");
        return;
      }

      currentSongIndex = index;
      const song = currentPlaylist[index];
      
      try {
        const apiUrl = `http://192.168.90.20:8050/Song_V1?ids=${song.id}&level=jyeffect&type=json`;
        const res = await fetch(apiUrl);
        if (!res.ok) {
          alert("获取音频资源失败");
          return;
        }
        const data = await res.json();

        if (!data.url) {
          alert("未找到音频资源");
          return;
        }

        const name = song.name;
        const artist = song.artists.map(a => a.name).join(', ');
        const cover = data.pic || '';
        const lyricRaw = data.lyric || '';

        const processedLyrics = lyricRaw
          .split('\n')
          .filter(line => line.trim() !== '')
          .join('\n');

        if (aplayer) {
          aplayer.destroy();
        }

        aplayer = new APlayer({
          container: document.getElementById('aplayer'),
          lrcType: 1,
          audio: [{
            name: name,
            artist: artist,
            url: data.url,
            cover: cover,
            lrc: processedLyrics
          }]
        });

        // 添加播放结束事件监听，自动播放下一首
        aplayer.on('ended', () => {
          const nextIndex = currentSongIndex + 1;
          if (nextIndex < currentPlaylist.length) {
            console.log(`自动播放下一首: ${currentPlaylist[nextIndex].name}`);
            playSong(nextIndex);
          } else {
            console.log("播放列表已结束");
          }
        });

        aplayer.play();
      } catch (e) {
        console.error(e);
        alert("播放失败，请检查控制台");
      }
    }

    function copyLink(link, btn) {
      navigator.clipboard.writeText(link).then(() => {
        const oldText = btn.textContent;
        btn.textContent = "已复制";
        setTimeout(() => btn.textContent = oldText, 1500);
      });
    }
  </script>
</body>
</html>