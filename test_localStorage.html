<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>测试localStorage歌单功能</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        button { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .add-btn { background: #28a745; color: white; }
        .remove-btn { background: #dc3545; color: white; }
        .clear-btn { background: #6c757d; color: white; }
        .playlist { margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 4px; }
        .song-item { padding: 8px; margin: 4px 0; background: white; border-radius: 4px; display: flex; justify-content: space-between; align-items: center; }
        .empty { text-align: center; color: #666; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <h1>localStorage 歌单功能测试</h1>
        
        <div>
            <h3>模拟歌曲</h3>
            <button class="add-btn" onclick="addSong(1, '青花瓷', '周杰伦')">添加：青花瓷 - 周杰伦</button>
            <button class="add-btn" onclick="addSong(2, '稻香', '周杰伦')">添加：稻香 - 周杰伦</button>
            <button class="add-btn" onclick="addSong(3, '夜曲', '周杰伦')">添加：夜曲 - 周杰伦</button>
        </div>
        
        <div class="playlist">
            <h3>我的歌单 <span id="count">(0首)</span></h3>
            <button class="clear-btn" onclick="clearPlaylist()">清空歌单</button>
            <div id="playlist-content">
                <div class="empty">歌单为空</div>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>操作说明</h3>
            <ul>
                <li>点击上方按钮添加歌曲到歌单</li>
                <li>歌单数据保存在浏览器的localStorage中</li>
                <li>刷新页面后歌单仍然存在</li>
                <li>只有清除浏览器缓存才会丢失数据</li>
                <li>可以点击歌曲旁的删除按钮移除单首歌曲</li>
            </ul>
        </div>
    </div>

    <script>
        // 加载歌单
        function loadPlaylist() {
            try {
                const saved = localStorage.getItem('musicPlaylist');
                const songs = saved ? JSON.parse(saved) : [];
                updatePlaylistUI(songs);
                return songs;
            } catch (error) {
                console.error('加载歌单失败:', error);
                return [];
            }
        }

        // 更新歌单UI
        function updatePlaylistUI(songs) {
            const countElem = document.getElementById('count');
            const contentElem = document.getElementById('playlist-content');
            
            countElem.textContent = `(${songs.length}首)`;
            
            if (songs.length === 0) {
                contentElem.innerHTML = '<div class="empty">歌单为空</div>';
                return;
            }
            
            contentElem.innerHTML = '';
            songs.forEach((song, index) => {
                const div = document.createElement('div');
                div.className = 'song-item';
                div.innerHTML = `
                    <span><strong>${song.name}</strong> - ${song.artists[0].name}</span>
                    <button class="remove-btn" onclick="removeSong(${song.id})">删除</button>
                `;
                contentElem.appendChild(div);
            });
        }

        // 添加歌曲
        function addSong(id, name, artist) {
            const songs = loadPlaylist();
            
            // 检查是否已存在
            if (songs.find(s => s.id === id)) {
                alert('歌曲已在歌单中！');
                return;
            }
            
            // 添加新歌曲
            const newSong = {
                id: id,
                name: name,
                artists: [{ name: artist }],
                album: { name: '测试专辑' },
                addedAt: new Date().toISOString()
            };
            
            songs.push(newSong);
            localStorage.setItem('musicPlaylist', JSON.stringify(songs));
            updatePlaylistUI(songs);
            
            alert(`已添加：${name} - ${artist}`);
        }

        // 删除歌曲
        function removeSong(songId) {
            const songs = loadPlaylist();
            const filteredSongs = songs.filter(s => s.id !== songId);
            
            if (filteredSongs.length < songs.length) {
                localStorage.setItem('musicPlaylist', JSON.stringify(filteredSongs));
                updatePlaylistUI(filteredSongs);
                alert('歌曲已删除！');
            } else {
                alert('未找到该歌曲！');
            }
        }

        // 清空歌单
        function clearPlaylist() {
            if (confirm('确定要清空歌单吗？')) {
                localStorage.setItem('musicPlaylist', JSON.stringify([]));
                updatePlaylistUI([]);
                alert('歌单已清空！');
            }
        }

        // 页面加载时初始化
        window.addEventListener('load', () => {
            loadPlaylist();
        });
    </script>
</body>
</html>
