<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>网易云音乐自定义播放器（Howler.js+歌词）</title>
  <script src="https://cdn.jsdelivr.net/npm/howler@2.2.4/dist/howler.min.js"></script>
  <style>
    body { background: #181d21; color: #e6e6e6; font-family: "Segoe UI", "微软雅黑", sans-serif; margin:0; padding:0; }
    .container { max-width: 700px; margin: 40px auto 0 auto; background: #22272b; border-radius: 8px; box-shadow: 0 2px 18px #0004; padding: 30px 35px 25px 35px;}
    h2 { text-align: center; margin: 0 0 18px 0; letter-spacing: 1px;}
    #searchBar { text-align:center; margin-bottom: 18px;}
    #keyword { width: 350px; padding: 7px 8px; font-size: 16px; border-radius: 4px; border: none; background: #282c34; color: #ccc;}
    #searchBtn { padding: 7px 18px; font-size: 16px; margin-left: 14px; cursor: pointer; border: none; border-radius: 4px; background: #09c; color: #fff;}
    #searchBtn:hover { background: #00d4ff; }
    #results { margin: 12px 0 0 0; border-radius: 6px; background: #23292f; }
    .song { display:flex; justify-content:space-between; align-items:center; padding:10px 15px; border-bottom: 1px solid #353c41; font-size: 15px;}
    .song:last-child { border-bottom: none;}
    .song-info { overflow: hidden; white-space: nowrap; text-overflow: ellipsis; max-width: 66%; color: #e6e6e6;}
    .song-info b { font-weight: 600; margin-right: 6px;}
    .btn-group button { margin-left: 8px; padding: 6px 13px; font-size: 14px; border: none; border-radius: 4px; background: #333c45; color: #d4d4d4; cursor: pointer;}
    .btn-group button:hover { background: #00d4ff; color: #222;}
    #pagination { margin-top: 12px; text-align:center; font-size: 15px;}
    #pagination button { padding: 6px 13px; margin: 0 10px; border: none; border-radius: 4px; background: #333c45; color: #d4d4d4;}
    #pagination button:disabled { opacity: 0.45; cursor: not-allowed;}
    #custom-player { background: #1b2128; border-radius: 8px; box-shadow: 0 2px 10px #111; padding: 18px 18px 10px 18px; margin: 24px 0 10px 0;}
    #song-info { font-weight: bold; font-size: 15px; margin-bottom: 8px; color: #eee; }
    #player-controls button { font-size: 18px; margin: 0 7px; background: #444; border: none; color: #fff; border-radius: 4px; padding: 5px 12px; cursor:pointer;}
    #player-controls button:hover { background: #00d4ff; color: #222;}
    #progress-bar { width:100%; height:8px; background:#555; border-radius:4px; margin:10px 0; cursor:pointer;}
    #progress { height:100%; width:0; background:#00d4ff; border-radius:4px; transition: width 0.2s;}
    #time-info { margin-left: 14px; font-size: 13px; color: #bbb;}
    #lyric-panel { background:#23292f;padding:15px 10px 15px 20px; min-height:60px; line-height:2; font-size:15px; border-radius:6px; margin-bottom:16px; max-height:120px; overflow:auto; color:#ccc;}
    #lyric-panel .current { color:#00d4ff; font-weight:bold; background:rgba(0,212,255,0.07);}

    /* 歌单相关样式 */
    #playlist-section { margin-top: 20px; background: #22272b; border-radius: 8px; padding: 20px; }
    #playlist-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
    #playlist-title { font-size: 18px; font-weight: bold; color: #e6e6e6; }
    #playlist-count { font-size: 14px; color: #999; }
    #playlist-toggle { padding: 6px 12px; font-size: 14px; border: none; border-radius: 4px; background: #333c45; color: #d4d4d4; cursor: pointer; }
    #playlist-toggle:hover { background: #00d4ff; color: #222; }
    #playlist-content { display: none; }
    #playlist-content.show { display: block; }
    #playlist-songs { background: #23292f; border-radius: 6px; max-height: 300px; overflow-y: auto; }
    .playlist-song { display: flex; justify-content: space-between; align-items: center; padding: 10px 15px; border-bottom: 1px solid #353c41; font-size: 14px; }
    .playlist-song:last-child { border-bottom: none; }
    .playlist-song:hover { background: #2a3038; }
    .playlist-song-info { flex: 1; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; color: #e6e6e6; }
    .playlist-song-info b { font-weight: 600; margin-right: 6px; }
    .playlist-song-time { font-size: 12px; color: #999; margin-right: 10px; }
    .playlist-song-actions button { margin-left: 5px; padding: 4px 8px; font-size: 12px; border: none; border-radius: 3px; background: #333c45; color: #d4d4d4; cursor: pointer; }
    .playlist-song-actions button:hover { background: #00d4ff; color: #222; }
    .empty-playlist { text-align: center; padding: 30px; color: #999; font-size: 14px; }
  </style>
</head>
<body>
<div class="container">
  <h2>网易云音乐播放器（自定义UI+歌词）</h2>
  <div id="searchBar">
    <input type="text" id="keyword" placeholder="请输入歌曲关键词" autocomplete="off" />
    <button id="searchBtn">搜索</button>
  </div>
  <div id="results"></div>
  <div id="pagination" style="display:none;">
    <button id="prevPageBtn">上一页</button>
    <span id="pageInfo"></span>
    <button id="nextPageBtn">下一页</button>
  </div>
  <div id="custom-player" style="display:none;">
    <div id="song-info">
      <span id="current-title"></span> - <span id="current-artist"></span>
    </div>
    <div id="player-controls">
      <button id="prev-btn">上一曲</button>
      <button id="play-btn">播放</button>
      <button id="pause-btn" style="display:none;">暂停</button>
      <button id="next-btn">下一曲</button>
      <span id="time-info">00:00 / 00:00</span>
    </div>
    <div id="progress-bar">
      <div id="progress"></div>
    </div>
  </div>
  <div id="lyric-panel"></div>

  <!-- 歌单区域 -->
  <div id="playlist-section">
    <div id="playlist-header">
      <div>
        <span id="playlist-title">我的歌单</span>
        <span id="playlist-count">(0首)</span>
      </div>
      <div>
        <button id="clear-playlist" style="margin-right: 8px;">清空歌单</button>
        <button id="playlist-toggle">显示歌单</button>
      </div>
    </div>
    <div id="playlist-content">
      <div id="playlist-songs">
        <div class="empty-playlist">歌单为空，搜索歌曲并添加到歌单吧！</div>
      </div>
    </div>
  </div>
</div>
<script>
let howlerSound = null;
let lyricArr = [];
let lyricTimer = null;
let duration = 0;
let currentPlaylist = [];
let currentSongIndex = 0;
let isUserScrolling = false; // 添加用户滚动状态标记
let scrollTimeout = null; // 滚动超时定时器

const lyricPanel = document.getElementById('lyric-panel');
const playBtn = document.getElementById('play-btn');
const pauseBtn = document.getElementById('pause-btn');
const prevBtn = document.getElementById('prev-btn');
const nextBtn = document.getElementById('next-btn');
const timeInfo = document.getElementById('time-info');
const progressBar = document.getElementById('progress-bar');
const progressElem = document.getElementById('progress');
const titleElem = document.getElementById('current-title');
const artistElem = document.getElementById('current-artist');
const customPlayer = document.getElementById('custom-player');

// 检测用户是否在滚动页面
let isPageScrolling = false;
let pageScrollTimeout = null;

window.addEventListener('scroll', function() {
  isPageScrolling = true;
  clearTimeout(pageScrollTimeout);
  pageScrollTimeout = setTimeout(() => {
    isPageScrolling = false;
  }, 15000); // 1秒后认为用户停止滚动
});

// 检测歌词面板内的滚动
lyricPanel.addEventListener('scroll', function() {
  isUserScrolling = true;
  clearTimeout(scrollTimeout);
  scrollTimeout = setTimeout(() => {
    isUserScrolling = false;
  }, 2000); // 2秒后认为用户停止滚动
});

// 调试函数（静默模式）
function debugLog(message) {
  // 只在控制台输出，不显示在页面上
  console.log(message);
}

// 搜索相关
let currentPage = 1;
let currentKeyword = '';
let lastResultCount = 0;

document.getElementById('searchBtn').addEventListener('click', () => search(1));
document.getElementById('keyword').addEventListener('keydown', e => {
  if (e.key === 'Enter') search(1);
});
document.getElementById('prevPageBtn').addEventListener('click', () => {
  if (currentPage > 1) search(currentPage - 1);
});
document.getElementById('nextPageBtn').addEventListener('click', () => {
  if (lastResultCount === 10) search(currentPage + 1);
});

async function search(page = 1) {
  const kw = document.getElementById("keyword").value.trim();
  if (!kw) { alert("请输入关键词"); return; }
  currentKeyword = kw;
  currentPage = page;
  document.getElementById('results').innerHTML = '';
  document.getElementById('pagination').style.display = 'none';

  try {
    const res = await fetch(`/search?q=${encodeURIComponent(kw)}&page=${page}`);
    if (!res.ok) { document.getElementById('results').innerHTML = "搜索失败，请稍后再试。"; return; }
    const songs = await res.json();
    lastResultCount = songs.length;
    currentPlaylist = songs;
    if (songs.length === 0) { document.getElementById('results').innerHTML = "没有找到歌曲"; return; }

    const resultsDiv = document.getElementById('results');
    resultsDiv.innerHTML = '';
    songs.forEach((song, i) => {
      const artists = song.artists.map(a => a.name).join(', ');
      const albumName = song.album && song.album.name ? song.album.name : '';
      const artistWithAlbum = albumName ? `${artists} (${albumName})` : artists;
      const link = `https://music.163.com?id=${song.id}`;
      const div = document.createElement('div');
      div.className = 'song';
      div.innerHTML = `
        <div class="song-info" title="${song.name} — ${artistWithAlbum}">
          <b>${song.name}</b> - ${artistWithAlbum}
        </div>
        <div class="btn-group">
          <button onclick="playSong(${i})">播放</button>
          <button onclick="addToPlaylist(${i})">加入歌单</button>
          <button onclick="copyLink('${link}', this)">复制链接</button>
        </div>
      `;
      resultsDiv.appendChild(div);
    });
    document.getElementById('pagination').style.display = 'block';
    document.getElementById('pageInfo').textContent = `第 ${currentPage} 页`;
    document.getElementById('prevPageBtn').disabled = currentPage === 1;
    document.getElementById('nextPageBtn').disabled = lastResultCount < 10;
  } catch (e) {
    console.error(e);
    document.getElementById('results').innerHTML = "搜索异常，请检查控制台";
  }
}

// 歌曲切换
function playSong(index) {
  if(index<0||index>=currentPlaylist.length) return;
  currentSongIndex = index;
  const song = currentPlaylist[index];
  
  fetch(`/play?id=${song.id}`)
    .then(res=>res.json())
    .then(data=>{
      if(howlerSound) {
        howlerSound.unload();
        clearInterval(lyricTimer);
      }
      
      // 获取歌词数据
      const lyricData = data.lyric || data.lrc || '';
      
      // 歌词解析
      lyricArr = parseLrc(lyricData);
      
      renderLyricPanel();
      titleElem.textContent = song.name;
      artistElem.textContent = song.artists.map(a=>a.name).join(', ');
      customPlayer.style.display = '';

      howlerSound = new Howl({
        src: [data.url],
        html5: true,
        onload: function() {
          duration = howlerSound.duration();
          updateTimeUI(0, duration);
        },
        onplay: function() {
          playBtn.style.display = 'none';
          pauseBtn.style.display = '';
          lyricTimer = setInterval(syncLyric, 300);
          updateProgress();
        },
        onpause: function() {
          playBtn.style.display = '';
          pauseBtn.style.display = 'none';
          clearInterval(lyricTimer);
        },
        onend: function() {
          playBtn.style.display = '';
          pauseBtn.style.display = 'none';
          clearInterval(lyricTimer);
          playNext();
        },
        onloaderror: function(id, error) {
          console.error(`音频加载错误: ${error}`);
        },
        onplayerror: function(id, error) {
          console.error(`播放错误: ${error}`);
        }
      });
      playBtn.style.display = 'none';
      pauseBtn.style.display = '';
      howlerSound.play();
    })
    .catch(error => {
      console.error('播放请求失败:', error);
    });
}

// 修复后的歌词解析函数
function parseLrc(lrc) {
  if (!lrc || typeof lrc !== 'string' || lrc.trim().length === 0) {
    return [];
  }
  
  const lines = lrc.split(/\r?\n/);
  const res = [];
  
  // 修正正则表达式：应该匹配 [mm:ss.xxx] 格式
  const timeRegex = /\[(\d{1,2}):(\d{2})(?:\.(\d{1,3}))?\]/g;
  
  for (const line of lines) {
    if (!line.trim()) continue;
    
    const timeMatches = [];
    let match;
    
    // 重置正则表达式的 lastIndex
    timeRegex.lastIndex = 0;
    
    // 找到所有时间戳
    while ((match = timeRegex.exec(line)) !== null) {
      timeMatches.push({
        min: parseInt(match[1]),
        sec: parseInt(match[2]),
        ms: match[3] ? parseInt(match[3].padEnd(3, '0')) : 0
      });
    }
    
    // 提取歌词文本（移除所有时间标签）
    const lrcText = line.replace(/\[\d{1,2}:\d{2}(?:\.\d{1,3})?\]/g, '').trim();
    
    // 如果有时间戳和歌词文本，添加到结果中
    if (timeMatches.length > 0 && lrcText) {
      timeMatches.forEach(timeData => {
        const time = timeData.min * 60 + timeData.sec + timeData.ms / 1000;
        res.push({ time, lrc: lrcText });
      });
    }
  }
  
  // 按时间排序
  res.sort((a, b) => a.time - b.time);
  
  return res;
}

// 歌词滚动高亮 - 修复版本
function syncLyric() {
  if(!howlerSound || !lyricArr.length) return;
  const t = howlerSound.seek();
  updateTimeUI(t, duration);
  
  // 找到当前应该高亮的歌词行
  let currentIndex = -1;
  for(let i = 0; i < lyricArr.length; i++) {
    if(t >= lyricArr[i].time) {
      currentIndex = i;
    } else {
      break;
    }
  }
  
  // 高亮当前行
  const lines = lyricPanel.querySelectorAll("div");
  lines.forEach((line, idx)=>{
    if(idx === currentIndex) {
      line.classList.add('current');
      // 只有在用户没有手动滚动歌词面板且页面没有在滚动时才自动滚动歌词
      if(!isUserScrolling && !isPageScrolling) {
        line.scrollIntoView({behavior:'smooth', block:'center'});
      }
    } else {
      line.classList.remove('current');
    }
  });
  updateProgress();
}

// 歌词渲染
function renderLyricPanel() {
  if(!lyricArr || lyricArr.length === 0) {
    lyricPanel.innerHTML = "<span style='color:#888;'>（无歌词）</span>";
    return;
  }
  lyricPanel.innerHTML = lyricArr.map(item=>`<div>${item.lrc}</div>`).join('');
}

// 进度条
function updateProgress() {
  if(!howlerSound) return;
  let cur = howlerSound.seek();
  let percent = duration ? cur/duration*100 : 0;
  progressElem.style.width = percent + "%";
}

// 时间显示
function updateTimeUI(cur, dur) {
  function fmt(t) {
    let m = Math.floor(t/60);
    let s = Math.floor(t%60);
    return (m<10?'0':'')+m+':'+(s<10?'0':'')+s;
  }
  timeInfo.textContent = fmt(cur) + ' / ' + fmt(dur||0);
}

// 控件事件
playBtn.onclick = ()=>{ if(howlerSound) howlerSound.play(); }
pauseBtn.onclick = ()=>{ if(howlerSound) howlerSound.pause(); }
prevBtn.onclick = playPrev;
nextBtn.onclick = playNext;

// 进度条拖动
progressBar.onclick = function(e){
  if(!howlerSound||!duration) return;
  const rect = progressBar.getBoundingClientRect();
  const percent = (e.clientX - rect.left)/rect.width;
  howlerSound.seek(percent*duration);
  updateProgress();
  syncLyric();
};

// 上一首
function playPrev() {
  if(currentSongIndex>0) playSong(currentSongIndex-1);
}
// 下一首
function playNext() {
  if(currentSongIndex+1<currentPlaylist.length) playSong(currentSongIndex+1);
}

// 复制链接
function copyLink(link, btn) {
  navigator.clipboard.writeText(link).then(() => {
    const oldText = btn.textContent;
    btn.textContent = "已复制";
    setTimeout(() => btn.textContent = oldText, 1300);
  }).catch(err => {
    alert('复制失败');
  });
}

// 歌单相关变量
let playlistSongs = [];
let isPlaylistVisible = false;

// 歌单相关函数
function addToPlaylist(index) {
  if (index < 0 || index >= currentPlaylist.length) return;
  const song = currentPlaylist[index];

  // 从localStorage加载现有歌单
  loadPlaylist();

  // 检查歌曲是否已存在
  const existingSong = playlistSongs.find(ps => ps.id === song.id);
  if (existingSong) {
    showMessage('歌曲已在歌单中', 'warning');
    return;
  }

  // 添加歌曲到歌单
  const playlistSong = {
    name: song.name,
    id: song.id,
    artists: song.artists,
    album: song.album,
    addedAt: new Date().toISOString()
  };

  playlistSongs.push(playlistSong);

  // 保存到localStorage
  localStorage.setItem('musicPlaylist', JSON.stringify(playlistSongs));

  showMessage('已添加到歌单', 'success');
  updatePlaylistUI();
}

function loadPlaylist() {
  try {
    const saved = localStorage.getItem('musicPlaylist');
    playlistSongs = saved ? JSON.parse(saved) : [];
    updatePlaylistUI();
  } catch (error) {
    console.error('加载歌单失败:', error);
    playlistSongs = [];
    updatePlaylistUI();
  }
}

function updatePlaylistUI() {
  const countElem = document.getElementById('playlist-count');
  const songsContainer = document.getElementById('playlist-songs');

  countElem.textContent = `(${playlistSongs.length}首)`;

  if (playlistSongs.length === 0) {
    songsContainer.innerHTML = '<div class="empty-playlist">歌单为空，搜索歌曲并添加到歌单吧！</div>';
    return;
  }

  songsContainer.innerHTML = '';
  playlistSongs.forEach((playlistSong, index) => {
    const artists = playlistSong.artists.map(a => a.name).join(', ');
    const albumName = playlistSong.album && playlistSong.album.name ? playlistSong.album.name : '';
    const artistWithAlbum = albumName ? `${artists} (${albumName})` : artists;
    const addedTime = new Date(playlistSong.addedAt).toLocaleDateString();

    const div = document.createElement('div');
    div.className = 'playlist-song';
    div.innerHTML = `
      <div class="playlist-song-info" title="${playlistSong.name} — ${artistWithAlbum}">
        <b>${playlistSong.name}</b> - ${artistWithAlbum}
      </div>
      <div class="playlist-song-time">${addedTime}</div>
      <div class="playlist-song-actions">
        <button onclick="playFromPlaylist(${index})">播放</button>
        <button onclick="removeFromPlaylist(${playlistSong.id})">删除</button>
      </div>
    `;
    songsContainer.appendChild(div);
  });
}

function removeFromPlaylist(songId) {
  // 从歌单中删除指定歌曲
  const originalLength = playlistSongs.length;
  playlistSongs = playlistSongs.filter(ps => ps.id !== songId);

  if (playlistSongs.length < originalLength) {
    // 保存到localStorage
    localStorage.setItem('musicPlaylist', JSON.stringify(playlistSongs));
    showMessage('已从歌单删除', 'success');
    updatePlaylistUI();
  } else {
    showMessage('歌单中未找到该歌曲', 'warning');
  }
}

function playFromPlaylist(index) {
  if (index < 0 || index >= playlistSongs.length) return;

  // 将歌单设置为当前播放列表
  currentPlaylist = playlistSongs.map(ps => ({
    name: ps.name,
    id: ps.id,
    artists: ps.artists,
    album: ps.album
  }));

  // 播放指定歌曲
  playSong(index);
}

function togglePlaylist() {
  const content = document.getElementById('playlist-content');
  const toggleBtn = document.getElementById('playlist-toggle');

  isPlaylistVisible = !isPlaylistVisible;

  if (isPlaylistVisible) {
    content.classList.add('show');
    toggleBtn.textContent = '隐藏歌单';
    loadPlaylist(); // 显示时加载歌单
  } else {
    content.classList.remove('show');
    toggleBtn.textContent = '显示歌单';
  }
}

function clearPlaylist() {
  if (playlistSongs.length === 0) {
    showMessage('歌单已经是空的', 'info');
    return;
  }

  if (confirm(`确定要清空歌单吗？这将删除所有 ${playlistSongs.length} 首歌曲。`)) {
    playlistSongs = [];
    localStorage.setItem('musicPlaylist', JSON.stringify(playlistSongs));
    updatePlaylistUI();
    showMessage('歌单已清空', 'success');
  }
}

function showMessage(message, type = 'info') {
  // 创建消息提示元素
  const messageDiv = document.createElement('div');
  messageDiv.textContent = message;
  messageDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px 15px;
    border-radius: 4px;
    color: white;
    font-size: 14px;
    z-index: 1000;
    transition: opacity 0.3s;
  `;

  // 根据类型设置颜色（暗黑主题）
  switch (type) {
    case 'success':
      messageDiv.style.backgroundColor = '#333c45';
      messageDiv.style.border = '1px solid #00d4ff';
      break;
    case 'warning':
      messageDiv.style.backgroundColor = '#333c45';
      messageDiv.style.border = '1px solid #666';
      messageDiv.style.color = '#e6e6e6';
      break;
    case 'error':
      messageDiv.style.backgroundColor = '#333c45';
      messageDiv.style.border = '1px solid #666';
      break;
    default:
      messageDiv.style.backgroundColor = '#333c45';
      messageDiv.style.border = '1px solid #00d4ff';
  }

  document.body.appendChild(messageDiv);

  // 3秒后自动消失
  setTimeout(() => {
    messageDiv.style.opacity = '0';
    setTimeout(() => {
      if (messageDiv.parentNode) {
        messageDiv.parentNode.removeChild(messageDiv);
      }
    }, 300);
  }, 3000);
}

// 页面加载完成后执行初始化
window.addEventListener('load', () => {
  console.log('页面加载完成');

  // 绑定歌单相关按钮事件
  document.getElementById('playlist-toggle').addEventListener('click', togglePlaylist);
  document.getElementById('clear-playlist').addEventListener('click', clearPlaylist);

  // 初始加载歌单（从localStorage）
  loadPlaylist();
});
</script>
</body>
</html>